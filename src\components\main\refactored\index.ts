/**
 * 重构组件的统一导出文件
 */

// 主容器组件
export { default as MainContainer } from './MainContainer';

// Hooks
export { useCesiumViewer } from './hooks/useCesiumViewer';
export { useSimulationState } from './hooks/useSimulationState';
export { useModalStates } from './hooks/useModalStates';

// 组件
export { default as CesiumViewport } from './components/CesiumViewport';
export { default as HeaderSection } from './components/HeaderSection';
export { default as LeftPanel } from './components/LeftPanel';
export { default as RightPanel } from './components/RightPanel';
export { default as LoadingOverlay } from './components/LoadingOverlay';
export { default as ModalsContainer } from './components/ModalsContainer';

// 类型
export type { CesiumViewerState, CesiumViewerActions } from './hooks/useCesiumViewer';
export type { SimulationState, SimulationActions } from './hooks/useSimulationState';
export type { ModalStates, ModalActions } from './hooks/useModalStates';

// 工具
export * from './utils/performanceUtils';
