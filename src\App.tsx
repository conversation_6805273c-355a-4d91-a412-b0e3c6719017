import React from "react";
// import CesiumComponent from "./components/main/main";
import MainContainer from './components/main/refactored/MainContainer';
import { BrowserRouter, Route, Routes } from "react-router-dom";
import "antd/dist/antd.css";

import TestComponent from "./test/test";
import BoxPreview from "./test/BoxPreview";
import SimpleTestPage from "./test/SimpleTestPage";
import ComprehensiveTestPage from "./test/ComprehensiveTestPage";
import BeamCellDemo from "./test/breamcell/BeamCellDemo";
function App() {
  return (
    <div style={{ height: "100vh", width: "100vw" }}>
      <BrowserRouter>
        <Routes>
          <Route
            path="/"
            element={<MainContainer />}
          ></Route>
          {/* <Route
            path="/"
            element={<CesiumComponent />}
          ></Route> */}
           <Route
            path="/test"
            element={<TestComponent />}
          ></Route>
          <Route
            path="/box-preview"
            element={<BoxPreview />}
          ></Route>
          <Route
            path="/logs-preview"
            element={<SimpleTestPage />}
          ></Route>
          <Route
            path="/comprehensive-test"
            element={<ComprehensiveTestPage />}
          ></Route>
          <Route
            path="/beam-cell-demo"
            element={<BeamCellDemo />}
          ></Route>
        </Routes>
      </BrowserRouter>
    </div>
  );
}

export default App;
